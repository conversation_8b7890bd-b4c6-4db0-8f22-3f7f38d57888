<template>
  <div class="remote-chat-loader">
    <!-- Chat4Loading样式的加载指示器 - 显示到CSR应用内容真正就绪 -->
    <div v-if="!contentReady && !error" class="chat4-loading visible">
      <!-- 背景遮罩 -->
      <div class="loading-backdrop"></div>

      <!-- 主要加载内容 -->
      <div class="loading-content">
        <!-- 头像容器 -->
        <div class="avatar-container">
          <div class="avatar-ring">
            <div class="avatar-ring-inner"></div>
            <img
              v-if="currentActor?.avatar_url"
              :src="currentActor.avatar_url"
              :alt="currentActor.name"
              class="character-avatar"
            />
            <div v-else class="avatar-placeholder">
              <div class="placeholder-content">
                <div class="avatar-silhouette"></div>
                <div class="loading-shimmer"></div>
              </div>
            </div>
          </div>

          <!-- 脉冲效果 -->
          <div class="pulse-ring pulse-ring-1"></div>
          <div class="pulse-ring pulse-ring-2"></div>
          <div class="pulse-ring pulse-ring-3"></div>
        </div>

        <!-- 加载文本 -->
        <div class="loading-text">
          <h3 class="loading-title">{{ loadingTitle }}</h3>
          <p class="loading-subtitle">{{ loadingSubtitle }}</p>
        </div>

        <!-- 进度指示器 -->
        <div class="progress-container">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: `${loadingProgress}%` }"
            ></div>
          </div>
          <div class="progress-dots">
            <div
              v-for="i in 3"
              :key="i"
              class="progress-dot"
              :class="{ active: loadingProgress / 33.33 >= i }"
            ></div>
          </div>
        </div>
      </div>

      <!-- 装饰性粒子 -->
      <div class="particles">
        <!-- 主要粒子轨道 -->
        <div
          v-for="i in 8"
          :key="`main-${i}`"
          class="particle main-particle"
          :style="{
            '--delay': `${i * 0.3}s`,
            '--angle': `${i * 45}deg`,
            '--radius': '140px',
            '--duration': '6s',
          }"
        ></div>

        <!-- 次要粒子轨道 -->
        <div
          v-for="i in 6"
          :key="`secondary-${i}`"
          class="particle secondary-particle"
          :style="{
            '--delay': `${i * 0.4}s`,
            '--angle': `${i * 60 + 30}deg`,
            '--radius': '100px',
            '--duration': '8s',
          }"
        ></div>

        <!-- 装饰小粒子 -->
        <div
          v-for="i in 12"
          :key="`tiny-${i}`"
          class="particle tiny-particle"
          :style="{
            '--delay': `${i * 0.15}s`,
            '--angle': `${i * 30}deg`,
            '--radius': '180px',
            '--duration': '10s',
          }"
        ></div>
      </div>
    </div>

    <!-- iframe 初始隐藏，就绪后显示 -->
    <iframe
      ref="chatIframe"
      :src="chatAppUrl"
      class="chat-iframe"
      :class="{ 'iframe-ready': iframeReady, 'iframe-hidden': !iframeReady }"
      frameborder="0"
      @load="onIframeLoad"
      @error="onIframeError"
    />

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <h2>Error Loading Chat</h2>
      <p>{{ error }}</p>
      <p class="error-url">尝试加载: {{ chatAppUrl }}</p>
      <button class="retry-button" @click="retryLoad">Retry</button>
      <button class="redirect-button" @click="redirectToChat"
        >Open in New Tab</button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  chatType: 'chat' | 'chat2' | 'chat3' | 'chat4'
  characterId?: string
  storyId?: string
}

const props = defineProps<Props>()

const route = useRoute()
const error = ref<string | null>(null)
const chatIframe = ref<HTMLIFrameElement | null>(null)
const iframeReady = ref(false)
const loadingProgress = ref(0)
const contentReady = ref(false)

// 引入 story store
const storyStore = useStoryStore()

// 从 store 获取当前角色信息
const currentActor = computed(() => storyStore.currentActor)

// 动态加载标题和副标题
const loadingTitle = computed(() => {
  if (currentActor.value?.name) {
    return `Connecting to ${currentActor.value.name}`
  }
  return 'Loading'
})

const loadingSubtitle = computed(() => {
  if (currentActor.value?.name) {
    return `Preparing your conversation with ${currentActor.value.name}...`
  }
  return 'Preparing your conversation...'
})

// 构建子应用URL
const chatAppUrl = computed(() => {
  const runtimeConfig = useRuntimeConfig()
  const baseUrl = runtimeConfig.public.csrAppUrl

  if (!baseUrl) {
    console.error(
      '❌ CSR App URL 未配置，请检查 NUXT_PUBLIC_CSR_APP_URL 环境变量',
    )
    return ''
  }

  let path = ''

  switch (props.chatType) {
    case 'chat':
    case 'chat2':
    case 'chat4':
      path = `/${props.chatType}/${props.storyId}/${props.characterId}`
      break
    case 'chat3':
      path = `/${props.chatType}/${props.characterId}/${props.storyId}`
      break
    default:
      path = `/chat/${props.storyId}/${props.characterId}`
  }

  // 不通过URL传递restart参数，改用postMessage避免iframe重新加载
  const fullUrl = `${baseUrl}${path}`

  return fullUrl
})

// iframe加载完成
const onIframeLoad = () => {
  error.value = null
  console.log('📱 iframe 加载完成，直接显示')

  // 简短延迟确保iframe内容渲染完成
  setTimeout(() => {
    showIframe()
  }, 300)

  // 如果需要restart，通过postMessage通知CSR应用
  const shouldRestart = route.query.restart === '1'

  if (shouldRestart) {
    // 延迟发送postMessage，确保iframe完全加载
    setTimeout(() => {
      const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
      if (iframe && iframe.contentWindow) {
        const runtimeConfig = useRuntimeConfig()
        const message = {
          type: 'RESTART_GAME',
          payload: { restart: true },
          timestamp: Date.now(),
          source: 'nuxt-app',
        }

        iframe.contentWindow.postMessage(
          message,
          runtimeConfig.public.csrAppUrl,
        )
      }
    }, 1000)

    // 立即清除URL中的restart参数，避免刷新时重复
    nextTick(() => {
      const currentQuery = { ...route.query }
      delete currentQuery.restart

      navigateTo({ query: currentQuery }, { replace: true })
    })
  }
}

// 显示 iframe
const showIframe = () => {
  if (iframeReady.value) return // 防止重复调用

  console.log('✅ 显示 iframe')
  iframeReady.value = true
  loadingProgress.value = 100

  // 发送成功显示事件用于监控
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'iframe_show_success', {
      event_category: 'performance',
      event_label: 'chat_iframe_ready',
    })
  }
}

// iframe加载错误
const onIframeError = () => {
  error.value =
    'Failed to load chat application. Please check if the chat service is running.'

  // 发送错误事件用于监控
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'iframe_load_error', {
      event_category: 'error',
      event_label: 'chat_iframe_error',
      value: 1,
    })
  }

  console.error('❌ iframe 加载失败:', chatAppUrl.value)
}

// 重试加载
const retryLoad = () => {
  error.value = null
  iframeReady.value = false
  loadingProgress.value = 0

  // 重新加载iframe
  const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
  if (iframe) {
    const currentSrc = iframe.src
    iframe.src = ''
    setTimeout(() => {
      iframe.src = currentSrc
    }, 100)
  }
}

// 重定向到新标签页
const redirectToChat = () => {
  window.open(chatAppUrl.value, '_blank')
}

// 处理来自CSR应用的消息
const handleMessage = (event: MessageEvent) => {
  // 基本消息格式验证
  if (!event.data || !event.data.type) {
    return
  }

  const { type, payload } = event.data

  switch (type) {
    case 'CONTENT_READY':
      // CSR应用内容已就绪
      console.log('✅ 主应用: CSR内容已就绪')
      contentReady.value = true
      showIframe()
      break
    case 'NAVIGATE_TO_HOME':
      // 导航到主应用首页
      navigateTo('/')
      break
    case 'NAVIGATE_TO_STORIES':
      // 导航到故事列表
      navigateTo('/stories')
      break
    case 'NAVIGATE_TO_STORY':
      // 导航到特定故事
      if (payload?.storyId) {
        navigateTo(`/story/${payload.storyId}`)
      }
      break
    case 'NAVIGATE_TO_NUXT_PAGE':
      // 导航到Nuxt中的其他页面
      if (payload?.path) {
        navigateTo(payload.path)
      }
      break
    case 'GO_BACK':
      // 返回上一页
      if (import.meta.client) {
        window.history.back()
      }
      break
    case 'SYNC_AUTH_STATE':
      // 同步认证状态
      handleAuthStateSync(payload)
      break
    case 'SYNC_USER_STATE':
      // 同步用户状态
      handleUserStateSync(payload)
      break
    case 'REQUEST_STATE':
      // CSR应用请求状态
      sendStateToCSR()
      break
    case 'REQUEST_STORY_DATA':
      // CSR应用请求故事数据
      handleStoryDataRequest(payload)
      break
    case 'SYNC_STORY_STATE':
      // 同步故事状态
      handleStoryStateSync(payload)
      break
    case 'SOCIAL_LOGIN_REQUEST':
      // 处理来自CSR应用的社交登录请求
      handleSocialLoginRequest(payload)
      break
    case 'LOADING_PROGRESS':
      // 接收CSR应用的加载进度（现在不需要处理）
      break
    case 'CSR_APP_READY':
      // CSR应用已就绪（现在不需要处理）
      break
    case 'CSR_ROUTE_READY':
      // CSR应用路由已就绪（现在不需要处理）
      break
    case 'PAYMENT_REDIRECT':
      // 处理来自CSR应用的支付重定向请求
      handlePaymentRedirect(payload)
      break
    default:
      console.warn('未知的消息类型:', type)
  }
}

// 状态同步处理函数
const handleAuthStateSync = (authData: any) => {
  console.log('🔐 主应用: 接收到认证状态同步', authData)

  // 这里可以更新主应用的认证状态
  // 例如：更新token到localStorage或store
  if (authData.token) {
    localStorage.setItem('token', authData.token)
  }
  if (authData.refreshToken) {
    localStorage.setItem('refreshToken', authData.refreshToken)
  }
  if (authData.userId) {
    localStorage.setItem('userId', authData.userId)
  }
}

const handleUserStateSync = (userData: any) => {
  console.log('👤 主应用: 接收到用户状态同步', userData)

  // 这里可以更新主应用的用户状态
  // 例如：更新用户信息到store
  if (userData.user) {
    localStorage.setItem('userInfo', JSON.stringify(userData.user))
  }
  if (userData.language) {
    localStorage.setItem('language', userData.language)
  }
  if (userData.theme) {
    localStorage.setItem('theme', userData.theme)
  }
}

// 处理来自CSR应用的社交登录请求
const handleSocialLoginRequest = async (payload: any) => {
  console.log('🔐 主应用: 接收到社交登录请求', payload)

  try {
    const { type, social_redirect_url, app_redirect_url } = payload

    // 存储重定向URL
    if (import.meta.client && app_redirect_url) {
      sessionStorage.setItem('login_redirect', app_redirect_url)
    }

    // 调用主应用的社交登录API
    const config = useRuntimeConfig()
    const response = await $fetch(
      `${config.public.apiBase}/api/v1/social-login.get-url`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          login_type: type,
          redirect_url: social_redirect_url,
        },
      },
    )

    if (response.code !== '0' || !response.data?.url) {
      throw new Error('Failed to get social login URL')
    }

    const loginUrl = response.data.url
    if (!loginUrl.startsWith('http')) {
      throw new Error('Invalid login URL')
    }

    // 在主应用中跳转到社交登录页面
    window.location.href = loginUrl
  } catch (error: any) {
    console.error('社交登录请求处理失败:', error)
    // 可以通过postMessage通知CSR应用登录失败
    const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
    if (iframe && iframe.contentWindow) {
      const runtimeConfig = useRuntimeConfig()
      iframe.contentWindow.postMessage(
        {
          type: 'SOCIAL_LOGIN_ERROR',
          error: error.message || 'Social login failed',
        },
        runtimeConfig.public.csrAppUrl,
      )
    }
  }
}

// 处理故事数据请求
const handleStoryDataRequest = async (payload: any) => {
  console.log('📚 主应用: 收到故事数据请求', payload)

  const { storyId, actorId } = payload

  try {
    // 使用 Nuxt 的 $fetch 来获取故事数据
    const config = useRuntimeConfig()
    const [storyResponse, actorResponse] = await Promise.all([
      $fetch(`${config.public.apiBase}/api/v1/stories/${storyId}`),
      $fetch(`${config.public.apiBase}/api/v1/stories/${storyId}/actors`),
    ])

    let story = null
    let actor = null

    if (storyResponse.code === '0') {
      story = storyResponse.data.story
    }

    if (actorResponse.code === '0') {
      actor = actorResponse.data.actors?.find((a: any) => a.id === actorId)
    }

    // 发送数据到CSR应用
    const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
    if (iframe && iframe.contentWindow) {
      iframe.contentWindow.postMessage(
        {
          type: 'STORY_DATA_RESPONSE',
          payload: { story, actor },
          timestamp: Date.now(),
          source: 'nuxt-app',
        },
        config.public.csrAppUrl,
      )
      console.log('📚 主应用: 已发送故事数据到CSR应用', { story, actor })
    }
  } catch (error) {
    console.error('获取故事数据失败:', error)
    // 发送错误响应
    const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
    if (iframe && iframe.contentWindow) {
      const config = useRuntimeConfig()
      iframe.contentWindow.postMessage(
        {
          type: 'STORY_DATA_ERROR',
          error: error.message || 'Failed to fetch story data',
          timestamp: Date.now(),
          source: 'nuxt-app',
        },
        config.public.csrAppUrl,
      )
    }
  }
}

// 处理故事状态同步
const handleStoryStateSync = (payload: any) => {
  console.log('📚 主应用: 收到故事状态同步', payload)

  // 这里可以将故事状态保存到主应用的状态管理中
  // 例如：存储到 localStorage 或 store
  if (payload.currentStory) {
    localStorage.setItem('currentStory', JSON.stringify(payload.currentStory))
  }
  if (payload.currentActor) {
    localStorage.setItem('currentActor', JSON.stringify(payload.currentActor))
  }
}

// 处理来自CSR应用的支付重定向请求
const handlePaymentRedirect = async (payload: any) => {
  console.log('💳 主应用: 收到支付重定向请求', payload)

  try {
    const { provider, redirectUrl, sessionId, useStripeSDK, stripePublicKey } =
      payload

    if (provider === 'stripe' && useStripeSDK && sessionId && stripePublicKey) {
      // 使用Stripe SDK处理支付
      try {
        // 动态导入 Stripe SDK
        const { loadStripe } = await import('@stripe/stripe-js')
        const stripe = await loadStripe(stripePublicKey)

        if (!stripe) {
          throw new Error('Failed to load Stripe SDK')
        }

        // 使用官方 SDK 重定向
        const { error } = await stripe.redirectToCheckout({
          sessionId: sessionId,
        })

        if (error) {
          throw new Error(error.message || 'Stripe redirect failed')
        }
      } catch (error) {
        console.error(
          '💳 主应用: 使用Stripe SDK重定向失败，降级到直接重定向',
          error,
        )
        // 降级到直接 URL 重定向
        window.location.href = `https://checkout.stripe.com/pay/${sessionId}`
      }
    } else if (redirectUrl) {
      // 其他支付方式使用直接重定向
      window.location.href = redirectUrl
    } else {
      throw new Error('No valid payment redirect information provided')
    }
  } catch (error: any) {
    console.error('💳 主应用: 支付重定向失败', error)

    // 通知CSR应用支付重定向失败
    const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
    if (iframe && iframe.contentWindow) {
      const config = useRuntimeConfig()
      iframe.contentWindow.postMessage(
        {
          type: 'PAYMENT_REDIRECT_ERROR',
          error: error.message || 'Payment redirect failed',
          timestamp: Date.now(),
          source: 'nuxt-app',
        },
        config.public.csrAppUrl,
      )
    }
  }
}

const sendStateToCSR = () => {
  console.log('📤 主应用: 发送状态到CSR应用')

  // 获取配置
  const config = useRuntimeConfig()

  // 获取主应用的状态
  const authState = {
    token: localStorage.getItem('token'),
    refreshToken: localStorage.getItem('refreshToken'),
    userId: localStorage.getItem('userId'),
  }

  const userState = {
    user: localStorage.getItem('userInfo')
      ? JSON.parse(localStorage.getItem('userInfo')!)
      : null,
    language: localStorage.getItem('language'),
    theme: localStorage.getItem('theme'),
  }

  const storyState = {
    currentStory: localStorage.getItem('currentStory')
      ? JSON.parse(localStorage.getItem('currentStory')!)
      : null,
    currentActor: localStorage.getItem('currentActor')
      ? JSON.parse(localStorage.getItem('currentActor')!)
      : null,
  }

  // 获取当前页面信息
  const pageState = {
    currentUrl: window.location.href,
    pathname: window.location.pathname,
    search: window.location.search,
    hash: window.location.hash,
  }

  console.log('📤 主应用: 准备发送的状态', {
    auth: authState,
    user: userState,
    story: storyState,
    page: pageState,
    targetUrl: config.public.csrAppUrl,
  })

  // 发送状态到CSR应用
  const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
  if (iframe && iframe.contentWindow) {
    console.log('📤 主应用: 找到iframe，发送消息')
    iframe.contentWindow.postMessage(
      {
        type: 'STATE_SYNC',
        payload: {
          auth: authState,
          user: userState,
          story: storyState,
          page: pageState,
        },
        timestamp: Date.now(),
        source: 'nuxt-app',
      },
      config.public.csrAppUrl,
    )
  } else {
    console.warn('❌ 主应用: 未找到iframe或iframe未加载完成')
  }
}

// 组件挂载时的事件监听
onMounted(() => {
  // 监听来自CSR应用的消息
  if (import.meta.client) {
    window.addEventListener('message', handleMessage)
  }

  // 立即检查iframe是否已经加载完成（处理刷新场景）
  nextTick(() => {
    const iframe = chatIframe.value
    if (iframe && iframe.src && !iframeReady.value) {
      // iframe已存在且有src，500ms后显示（给iframe渲染时间）
      setTimeout(() => {
        if (!iframeReady.value) {
          console.log('📱 组件挂载时检测：iframe已就绪，直接显示')
          showIframe()
        }
      }, 500)
    }
  })

  // 超时保护：如果5秒内没有收到内容就绪信号，强制显示
  setTimeout(() => {
    if (!contentReady.value) {
      console.warn('⚠️ 超时保护：强制显示内容')
      contentReady.value = true
      showIframe()
    }
  }, 5000)

  // 延迟发送初始状态到CSR应用
  setTimeout(() => {
    sendStateToCSR()
  }, 1000)

  // 监听登录状态变化，当从第三方登录回来时重新同步状态
  if (import.meta.client) {
    const checkAuthStateChange = () => {
      const needSync = localStorage.getItem('needSyncToCSR')
      const currentToken = localStorage.getItem('token')
      const currentUserId = localStorage.getItem('userId')

      // 如果有同步标记或检测到新的登录状态，重新发送状态到CSR应用
      if (needSync === 'true' || (currentToken && currentUserId)) {
        console.log('🔄 检测到需要同步状态到CSR应用')

        // 清除同步标记
        if (needSync === 'true') {
          localStorage.removeItem('needSyncToCSR')
        }

        setTimeout(() => {
          sendStateToCSR()
        }, 500)
      }
    }

    // 立即检查一次
    checkAuthStateChange()

    // 页面获得焦点时检查登录状态（从第三方登录回来时会触发）
    window.addEventListener('focus', checkAuthStateChange)

    // 页面可见性变化时检查登录状态
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        checkAuthStateChange()
      }
    })

    // 定期检查是否需要同步（作为备用机制）
    const syncCheckInterval = setInterval(() => {
      const needSync = localStorage.getItem('needSyncToCSR')
      if (needSync === 'true') {
        console.log('🔄 定期检查发现需要同步状态')
        checkAuthStateChange()
      }
    }, 2000)

    // 组件卸载时清理定时器
    onUnmounted(() => {
      clearInterval(syncCheckInterval)
    })
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  if (import.meta.client) {
    window.removeEventListener('message', handleMessage)
  }
})

// 监听props变化，更新URL
watch(
  [() => props.chatType, () => props.storyId, () => props.characterId],
  () => {
    error.value = null
  },
)
</script>

<style scoped>
.remote-chat-loader {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  overflow: hidden;
}

.chat-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #000;
  transition: opacity 0.3s ease-in-out;
}

.chat-iframe.iframe-hidden {
  opacity: 0;
  pointer-events: none;
}

.chat-iframe.iframe-ready {
  opacity: 1;
  pointer-events: auto;
}

/* Chat4Loading样式 */
.chat4-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  z-index: 9500;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat4-loading.visible {
  opacity: 1;
  visibility: visible;
}

.loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(31, 0, 56, 0.95) 0%,
    rgba(76, 60, 89, 0.95) 50%,
    rgba(124, 77, 255, 0.95) 100%
  );
  backdrop-filter: blur(20px);
}

.loading-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
  padding: 40px 20px;
}

/* 头像容器 */
.avatar-container {
  position: relative;
  margin-bottom: 32px;
}

.avatar-ring {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(45deg, #7c4dff, #ff4081, #00bcd4);
  padding: 4px;
  animation: rotate 3s linear infinite;
}

.avatar-ring-inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #1f0038;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.character-avatar {
  width: calc(100% - 16px);
  height: calc(100% - 16px);
  border-radius: 50%;
  object-fit: cover;
  position: relative;
  z-index: 2;
}

.avatar-placeholder {
  width: calc(100% - 16px);
  height: calc(100% - 16px);
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(124, 77, 255, 0.2),
    rgba(255, 64, 129, 0.2)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.placeholder-content {
  position: relative;
  width: 60%;
  height: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-silhouette {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  position: relative;
}

.avatar-silhouette::before {
  content: '';
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 40%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.avatar-silhouette::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 50%;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50% 50% 0 0;
}

.loading-shimmer {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer-avatar 2s ease-in-out infinite;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(124, 77, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s ease-out infinite;
}

.pulse-ring-1 {
  width: 140px;
  height: 140px;
  animation-delay: 0s;
}

.pulse-ring-2 {
  width: 160px;
  height: 160px;
  animation-delay: 0.7s;
}

.pulse-ring-3 {
  width: 180px;
  height: 180px;
  animation-delay: 1.4s;
}

/* 加载文本 */
.loading-text {
  text-align: center;
  margin-bottom: 32px;
}

.loading-title {
  font-family: 'Work Sans', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
  letter-spacing: 0.5px;
}

.loading-subtitle {
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
}

/* 进度指示器 */
.progress-container {
  width: 240px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 16px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #7c4dff, #ff4081);
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
  animation: shimmer 1.5s ease-in-out infinite;
}

.progress-dots {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.progress-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.progress-dot.active {
  background: #7c4dff;
  box-shadow: 0 0 12px rgba(124, 77, 255, 0.6);
  transform: scale(1.2);
}

/* 装饰性粒子 */
.particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  height: 400px;
  pointer-events: none;
}

.particle {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  transform-origin: 0 0;
  animation: orbit var(--duration) linear infinite;
  animation-delay: var(--delay);
  transform: translate(-50%, -50%) rotate(var(--angle))
    translateY(calc(-1 * var(--radius))) rotate(calc(-1 * var(--angle)));
}

.main-particle {
  width: 6px;
  height: 6px;
  background: radial-gradient(
    circle,
    rgba(124, 77, 255, 0.8),
    rgba(124, 77, 255, 0.3)
  );
  box-shadow:
    0 0 10px rgba(124, 77, 255, 0.5),
    0 0 20px rgba(124, 77, 255, 0.2);
}

.main-particle::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  width: 10px;
  height: 10px;
  background: radial-gradient(circle, rgba(255, 64, 129, 0.6), transparent 70%);
  border-radius: 50%;
  animation: pulse-glow 3s ease-in-out infinite alternate;
}

.secondary-particle {
  width: 4px;
  height: 4px;
  background: radial-gradient(
    circle,
    rgba(255, 64, 129, 0.7),
    rgba(255, 64, 129, 0.2)
  );
  box-shadow:
    0 0 8px rgba(255, 64, 129, 0.4),
    0 0 16px rgba(255, 64, 129, 0.1);
}

.secondary-particle::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(0, 188, 212, 0.5), transparent 60%);
  border-radius: 50%;
  animation: twinkle 2s ease-in-out infinite alternate;
}

.tiny-particle {
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
  animation-direction: reverse;
}

.tiny-particle::before {
  content: '';
  position: absolute;
  top: -0.5px;
  left: -0.5px;
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, rgba(124, 77, 255, 0.4), transparent 50%);
  border-radius: 50%;
  animation: sparkle 1.5s ease-in-out infinite;
}

/* 动画定义 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-20px);
  }
  100% {
    transform: translateX(240px);
  }
}

@keyframes orbit {
  from {
    transform: translate(-50%, -50%) rotate(var(--angle))
      translateY(calc(-1 * var(--radius))) rotate(calc(-1 * var(--angle)));
  }
  to {
    transform: translate(-50%, -50%) rotate(calc(var(--angle) + 360deg))
      translateY(calc(-1 * var(--radius)))
      rotate(calc(-1 * (var(--angle) + 360deg)));
  }
}

@keyframes pulse-glow {
  0% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
}

@keyframes twinkle {
  0% {
    opacity: 0.2;
    transform: scale(0.5) rotate(0deg);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.3) rotate(180deg);
  }
  100% {
    opacity: 0.2;
    transform: scale(0.5) rotate(360deg);
  }
}

@keyframes sparkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

@keyframes shimmer-avatar {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* 错误状态保持原有样式 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  z-index: 10;
}

.error-overlay h2 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.error-overlay p {
  color: #ccc;
  text-align: center;
  margin-bottom: 1.5rem;
  max-width: 400px;
}

.retry-button,
.redirect-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  margin: 0.5rem;
  transition: background-color 0.2s;
}

.retry-button:hover,
.redirect-button:hover {
  background: #2980b9;
}

.redirect-button {
  background: #27ae60;
}

.redirect-button:hover {
  background: #229954;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .loading-content {
    padding: 20px;
  }

  .avatar-ring {
    width: 100px;
    height: 100px;
  }

  .pulse-ring-1 {
    width: 120px;
    height: 120px;
  }
  .pulse-ring-2 {
    width: 140px;
    height: 140px;
  }
  .pulse-ring-3 {
    width: 160px;
    height: 160px;
  }

  .loading-title {
    font-size: 20px;
  }

  .progress-container {
    width: 200px;
  }

  .particles {
    width: 300px;
    height: 300px;
  }

  .main-particle {
    width: 4px;
    height: 4px;
  }

  .secondary-particle {
    width: 3px;
    height: 3px;
  }
}
</style>
